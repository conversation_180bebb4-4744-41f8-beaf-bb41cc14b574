{"name": "task-organizer", "version": "1.0.0", "description": "AI-powered task organization using Gemini 2.0 Flash", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "task-management", "gemini", "productivity", "organization"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}
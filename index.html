<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Organizer - Gemini 2.0 Flash AI Task Structuring</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎯 Task Organizer</h1>
            <p>Transform messy task notes into structured, prioritized action items using Gemini 2.0 Flash AI</p>
        </header>

        <div class="api-key-section">
            <label for="apiKey">Gemini API Key:</label>
            <input type="password" id="apiKey" placeholder="Enter your Gemini API key" />
            <small>Your API key is stored locally and never sent to our servers</small>
        </div>

        <div class="input-section">
            <h2>📝 Raw Task Notes</h2>
            <textarea 
                id="taskInput" 
                placeholder="Paste your messy task notes here...

Example:
- call with vendor, need to confirm delivery timelines again by EOD
- frontend layout broken on mobile view, revisit padding + responsiveness  
- Send client the latest mockups for review asap, she asked yesterday
- Meeting with marketing to brainstorm next month's campaign ideas
- UAT for CRM updates needs retesting; user reported a bug again"
                rows="10"
            ></textarea>
            <button id="processBtn" onclick="processTasks()">🚀 Structure & Prioritize Tasks</button>
        </div>

        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p>AI is organizing your tasks...</p>
        </div>

        <div id="results" class="results hidden">
            <div class="results-header">
                <h2>✅ Organized Tasks</h2>
                <div class="export-buttons">
                    <button onclick="exportJSON()">📄 Export JSON</button>
                    <button onclick="exportCSV()">📊 Export CSV</button>
                </div>
            </div>
            <div id="taskList"></div>
        </div>

        <div id="error" class="error hidden"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>

let processedTasks = [];

// Load API key from localStorage
document.addEventListener('DOMContentLoaded', function() {
    const savedApiKey = localStorage.getItem('geminiApiKey');
    if (savedApiKey) {
        document.getElementById('apiKey').value = savedApiKey;
    }
});

// Save API key to localStorage when changed
document.getElementById('apiKey').addEventListener('change', function() {
    localStorage.setItem('geminiApiKey', this.value);
});

async function processTasks() {
    const apiKey = document.getElementById('apiKey').value.trim();
    const taskInput = document.getElementById('taskInput').value.trim();
    
    // Validation
    if (!apiKey) {
        showError('Please enter your Gemini API key');
        return;
    }
    
    if (!taskInput) {
        showError('Please enter some task notes to process');
        return;
    }
    
    // Show loading state
    showLoading();
    hideError();
    hideResults();
    
    try {
        const structuredTasks = await callGeminiAPI(apiKey, taskInput);
        processedTasks = structuredTasks;
        displayResults(structuredTasks);
        hideLoading();
    } catch (error) {
        hideLoading();
        showError(`Error processing tasks: ${error.message}`);
    }
}

async function callGeminiAPI(apiKey, taskNotes) {
    const prompt = `You are an intelligent task assistant helping a busy project manager organize their raw, unstructured task notes.

The user will input a list of messy, informal task descriptions — usually copied from meeting notes, phone calls, chats, or client requests. These tasks are unformatted and may vary in clarity or tone.

Your job is to clean, structure, and enrich each task for project tracking.

🔶 For every task, perform the following:

1. **Summarize** it into a short and clear actionable sentence (10–20 words max).
2. **Add 1–2 relevant hashtags** as tags (e.g., from this pool:  
   #urgent, #client, #frontend, #backend, #QA, #bug, #meeting, #finance, #documentation, #api, #review, #low)
   - Use hashtags based on the task's content.
3. **Assign a priority score** between 1–5:
   - 5 = Extremely urgent or business-critical
   - 4 = High priority
   - 3 = Normal priority
   - 2 = Low importance
   - 1 = Trivial or long-term

🔶 Output Structure:

Respond with ONLY a valid JSON array of objects in this format:
[
  {
    "summary": "Clean, concise task summary",
    "tags": ["#tag1", "#tag2"],
    "priority": 3
  }
]

📋 Task List to Process:

${taskNotes}

Return ONLY the JSON array. Do NOT include any explanations, markdown, comments, or formatting — only output the JSON array.`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.2,
                topK: 32,
                topP: 0.9,
                maxOutputTokens: 4096,
            }
        })
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response from Gemini API');
    }
    
    const generatedText = data.candidates[0].content.parts[0].text;
    
    try {
        // Clean the response to extract JSON
        const jsonMatch = generatedText.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
            throw new Error('No JSON array found in response');
        }
        
        const tasks = JSON.parse(jsonMatch[0]);
        
        if (!Array.isArray(tasks)) {
            throw new Error('Response is not an array');
        }
        
        return tasks;
    } catch (parseError) {
        console.error('Raw response:', generatedText);
        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
    }
}

function displayResults(tasks) {
    const taskList = document.getElementById('taskList');
    taskList.innerHTML = '';
    
    // Sort tasks by priority (highest first)
    const sortedTasks = [...tasks].sort((a, b) => b.priority - a.priority);
    
    sortedTasks.forEach((task, index) => {
        const taskElement = document.createElement('div');
        taskElement.className = 'task-item';
        
        const tagsHtml = task.tags.map(tag => `<span class="tag">${tag}</span>`).join('');
        const priorityText = getPriorityText(task.priority);
        
        taskElement.innerHTML = `
            <div class="task-summary">${task.summary}</div>
            <div class="task-meta">
                <div class="task-tags">${tagsHtml}</div>
                <div class="priority priority-${task.priority}">Priority ${task.priority} - ${priorityText}</div>
            </div>
        `;
        
        taskList.appendChild(taskElement);
    });
    
    showResults();
}

function getPriorityText(priority) {
    const priorityTexts = {
        5: 'Critical',
        4: 'High',
        3: 'Normal',
        2: 'Low',
        1: 'Trivial'
    };
    return priorityTexts[priority] || 'Unknown';
}

function exportJSON() {
    const dataStr = JSON.stringify(processedTasks, null, 2);
    downloadFile(dataStr, 'tasks.json', 'application/json');
}

function exportCSV() {
    const headers = ['Summary', 'Tags', 'Priority'];
    const csvRows = [headers.join(',')];
    
    processedTasks.forEach(task => {
        const row = [
            `"${task.summary.replace(/"/g, '""')}"`,
            `"${task.tags.join(', ')}"`,
            task.priority
        ];
        csvRows.push(row.join(','));
    });
    
    const csvContent = csvRows.join('\n');
    downloadFile(csvContent, 'tasks.csv', 'text/csv');
}

function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function showLoading() {
    document.getElementById('loading').classList.remove('hidden');
    document.getElementById('processBtn').disabled = true;
}

function hideLoading() {
    document.getElementById('loading').classList.add('hidden');
    document.getElementById('processBtn').disabled = false;
}

function showResults() {
    document.getElementById('results').classList.remove('hidden');
}

function hideResults() {
    document.getElementById('results').classList.add('hidden');
}

function showError(message) {
    const errorDiv = document.getElementById('error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

function hideError() {
    document.getElementById('error').classList.add('hidden');
}

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            connectSrc: ["'self'", "https://generativelanguage.googleapis.com"],
        },
    },
}));

// CORS configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'production' ? false : true,
    credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// API endpoint to process tasks
app.post('/api/process-tasks', async (req, res) => {
    try {
        const { taskNotes } = req.body;
        
        if (!taskNotes || typeof taskNotes !== 'string') {
            return res.status(400).json({ 
                error: 'Task notes are required and must be a string' 
            });
        }

        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            return res.status(500).json({ 
                error: 'Gemini API key not configured on server' 
            });
        }

        const prompt = `You are an intelligent task assistant helping a busy project manager organize their raw, unstructured task notes.

The user will input a list of messy, informal task descriptions — usually copied from meeting notes, phone calls, chats, or client requests. These tasks are unformatted and may vary in clarity or tone.

Your job is to clean, structure, and enrich each task for project tracking.

🔶 For every task, perform the following:

1. **Summarize** it into a short and clear actionable sentence (10–20 words max).
2. **Add 1–2 relevant hashtags** as tags (e.g., from this pool:  
   #urgent, #client, #frontend, #backend, #QA, #bug, #meeting, #finance, #documentation, #api, #review, #low)
   - Use hashtags based on the task's content.
3. **Assign a priority score** between 1–5:
   - 5 = Extremely urgent or business-critical
   - 4 = High priority
   - 3 = Normal priority
   - 2 = Low importance
   - 1 = Trivial or long-term

🔶 Output Structure:

Respond with ONLY a valid JSON array of objects in this format:
[
  {
    "summary": "Clean, concise task summary",
    "tags": ["#tag1", "#tag2"],
    "priority": 3
  }
]

📋 Task List to Process:

${taskNotes}

Return ONLY the JSON array. Do NOT include any explanations, markdown, comments, or formatting — only output the JSON array.`;

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.2,
                    topK: 32,
                    topP: 0.9,
                    maxOutputTokens: 4096,
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('Invalid response from Gemini API');
        }
        
        const generatedText = data.candidates[0].content.parts[0].text;
        
        try {
            // Clean the response to extract JSON
            const jsonMatch = generatedText.match(/\[[\s\S]*\]/);
            if (!jsonMatch) {
                throw new Error('No JSON array found in response');
            }
            
            const tasks = JSON.parse(jsonMatch[0]);
            
            if (!Array.isArray(tasks)) {
                throw new Error('Response is not an array');
            }
            
            res.json({ tasks });
        } catch (parseError) {
            console.error('Raw response:', generatedText);
            throw new Error(`Failed to parse JSON response: ${parseError.message}`);
        }

    } catch (error) {
        console.error('Error processing tasks:', error);
        res.status(500).json({ 
            error: `Failed to process tasks: ${error.message}` 
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        apiKeyConfigured: !!process.env.GEMINI_API_KEY
    });
});

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

app.listen(PORT, () => {
    console.log(`🚀 Task Organizer server running on port ${PORT}`);
    console.log(`📝 Open http://localhost:${PORT} to use the application`);
    console.log(`🔑 API Key configured: ${!!process.env.GEMINI_API_KEY}`);
});

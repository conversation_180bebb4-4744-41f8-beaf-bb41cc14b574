* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

.api-key-section {
    padding: 20px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.api-key-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.api-key-section input {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.api-key-section input:focus {
    outline: none;
    border-color: #4facfe;
}

.api-key-section small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 12px;
}

.input-section {
    padding: 30px;
}

.input-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5em;
}

#taskInput {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 14px;
    font-family: 'Courier New', monospace;
    resize: vertical;
    transition: border-color 0.3s;
}

#taskInput:focus {
    outline: none;
    border-color: #4facfe;
}

#processBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

#processBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

#processBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading {
    text-align: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results {
    padding: 30px;
    background: #f8f9fa;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-header h2 {
    color: #333;
    font-size: 1.5em;
}

.export-buttons {
    display: flex;
    gap: 10px;
}

.export-buttons button {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.export-buttons button:hover {
    background: #218838;
}

.task-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 5px solid #4facfe;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s;
}

.task-item:hover {
    transform: translateY(-2px);
}

.task-summary {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.task-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.priority {
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
}

.priority-5 { background: #dc3545; color: white; }
.priority-4 { background: #fd7e14; color: white; }
.priority-3 { background: #ffc107; color: #212529; }
.priority-2 { background: #20c997; color: white; }
.priority-1 { background: #6c757d; color: white; }

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 20px;
    margin: 20px 30px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
}

.hidden {
    display: none;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    header {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .input-section, .results {
        padding: 20px;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .export-buttons {
        justify-content: center;
    }
}

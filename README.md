# 🎯 Task Organizer - AI-Powered Task Structuring

Transform messy, unstructured task notes into clean, prioritized action items using Google's Gemini 2.0 Flash AI model.

## ✨ Features

- **AI-Powered Processing**: Uses Gemini 2.0 Flash API for fast, intelligent parsing and structuring
- **Smart Prioritization**: Automatically assigns priority scores (1-5) based on urgency and importance
- **Intelligent Tagging**: Adds relevant hashtags (#urgent, #client, #frontend, #backend, etc.)
- **Export Options**: Download results as JSON or CSV
- **Clean Interface**: Modern, responsive web design
- **Local Storage**: API key is stored securely in your browser

## 🚀 Quick Start

### 1. Get a Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key for use in the application

### 2. Run the Application
1. Clone or download this repository
2. Open `index.html` in your web browser
3. Enter your Gemini API key in the provided field
4. Paste your messy task notes in the text area
5. Click "Structure & Prioritize Tasks"

### 3. Example Input
```
call with vendor, need to confirm delivery timelines again by EOD
frontend layout broken on mobile view, revisit padding + responsiveness  
Send client the latest mockups for review asap, she asked yesterday
Meeting with marketing to brainstorm next month's campaign ideas
UAT for CRM updates needs retesting; user reported a bug again
```

### 4. Example Output
The AI will transform your messy notes into structured tasks with:
- **Clear summaries** (10-20 words each)
- **Relevant tags** (#urgent, #client, #frontend, #bug, etc.)
- **Priority scores** (1=Trivial to 5=Critical)

## 📁 File Structure

```
task-organizer/
├── index.html          # Main application interface
├── style.css           # Styling and responsive design
├── script.js           # Core functionality and API integration
└── README.md           # This file
```

## 🔧 How It Works

1. **Input Processing**: Takes raw, unstructured task notes
2. **AI Analysis**: Sends data to Gemini API with specialized prompt
3. **Smart Structuring**: AI cleans, summarizes, and categorizes each task
4. **Priority Assignment**: Automatically assigns priority based on urgency indicators
5. **Tag Generation**: Adds relevant hashtags for easy filtering
6. **Export Options**: Provides JSON and CSV download options

## 🏷️ Available Tags

The AI automatically selects from these common project tags:
- `#urgent` - Time-sensitive tasks
- `#client` - Client-related work
- `#frontend` - UI/UX development
- `#backend` - Server-side development
- `#QA` - Quality assurance and testing
- `#bug` - Bug fixes and issues
- `#meeting` - Meetings and discussions
- `#finance` - Financial and budget items
- `#documentation` - Documentation tasks
- `#api` - API development
- `#review` - Review and approval tasks
- `#low` - Low priority items

## 🔒 Privacy & Security

- **Local Processing**: Your API key is stored only in your browser's local storage
- **No Data Storage**: Task data is not stored on any external servers
- **Direct API Calls**: Communication goes directly from your browser to Google's Gemini API

## 🛠️ Customization

You can easily customize the application by:
- Modifying the tag list in the prompt (script.js)
- Adjusting priority criteria
- Changing the UI styling (style.css)
- Adding new export formats

## 📋 API Usage

The application uses Google's **Gemini 2.0 Flash** model via REST API for faster processing and better performance. Make sure you have:
- A valid Google account
- Access to Google AI Studio
- An active API key with Gemini API access

### Why Gemini 2.0 Flash?
- **Faster Processing**: Significantly quicker response times
- **Better Accuracy**: Improved understanding of task context and priorities
- **Cost Effective**: More efficient token usage
- **Enhanced Reasoning**: Better at categorizing and prioritizing complex tasks

## 🐛 Troubleshooting

**API Key Issues:**
- Ensure your API key is valid and has Gemini API access
- Check that you haven't exceeded your API quota

**Processing Errors:**
- Make sure your task notes contain actual tasks (not empty text)
- Try breaking down very long task lists into smaller chunks

**Export Issues:**
- Ensure you've processed tasks before trying to export
- Check that your browser allows file downloads

## 📄 License

This project is open source and available under the MIT License.

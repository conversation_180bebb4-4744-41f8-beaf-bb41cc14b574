# 🎯 Task Organizer - AI-Powered Task Structuring

Transform messy, unstructured task notes into clean, prioritized action items using Google's Gemini 2.0 Flash AI model.

## ✨ Features

- **AI-Powered Processing**: Uses Gemini 2.0 Flash API for fast, intelligent parsing and structuring
- **Smart Prioritization**: Automatically assigns priority scores (1-5) based on urgency and importance
- **Intelligent Tagging**: Adds relevant hashtags (#urgent, #client, #frontend, #backend, etc.)
- **Export Options**: Download results as JSON or CSV
- **Clean Interface**: Modern, responsive web design
- **Local Storage**: API key is stored securely in your browser

## 🚀 Quick Start

### 1. Get a Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key for the next step

### 2. Environment Setup
1. Clone or download this repository
2. Copy `.env.example` to `.env`:
   ```bash
cp .env.example .env
```
3. Edit `.env` and replace `your_gemini_api_key_here` with your actual API key:
   ```
GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Install Dependencies
```bash
npm install
```

### 4. Run the Application
```bash
npm start
```
The server will start on http://localhost:3000

### 5. Use the Application
1. Open http://localhost:3000 in your web browser
2. Paste your messy task notes in the text area
3. Click "Structure & Prioritize Tasks"
4. Export results as JSON or CSV

### 3. Example Input
```
call with vendor, need to confirm delivery timelines again by EOD
frontend layout broken on mobile view, revisit padding + responsiveness  
Send client the latest mockups for review asap, she asked yesterday
Meeting with marketing to brainstorm next month's campaign ideas
UAT for CRM updates needs retesting; user reported a bug again
```

### 4. Example Output
The AI will transform your messy notes into structured tasks with:
- **Clear summaries** (10-20 words each)
- **Relevant tags** (#urgent, #client, #frontend, #bug, etc.)
- **Priority scores** (1=Trivial to 5=Critical)

## 📁 File Structure

```
task-organizer/
├── public/             # Frontend files
│   ├── index.html      # Main application interface
│   ├── style.css       # Styling and responsive design
│   └── script.js       # Frontend JavaScript
├── server.js           # Express server with API endpoints
├── package.json        # Node.js dependencies and scripts
├── vercel.json         # Vercel deployment configuration
├── .env.example        # Environment variables template
├── .env                # Your environment variables (not in git)
├── .gitignore          # Git ignore rules
└── README.md           # This file
```

## 🔧 How It Works

1. **Input Processing**: Takes raw, unstructured task notes
2. **AI Analysis**: Sends data to Gemini API with specialized prompt
3. **Smart Structuring**: AI cleans, summarizes, and categorizes each task
4. **Priority Assignment**: Automatically assigns priority based on urgency indicators
5. **Tag Generation**: Adds relevant hashtags for easy filtering
6. **Export Options**: Provides JSON and CSV download options

## 🏷️ Available Tags

The AI automatically selects from these common project tags:
- `#urgent` - Time-sensitive tasks
- `#client` - Client-related work
- `#frontend` - UI/UX development
- `#backend` - Server-side development
- `#QA` - Quality assurance and testing
- `#bug` - Bug fixes and issues
- `#meeting` - Meetings and discussions
- `#finance` - Financial and budget items
- `#documentation` - Documentation tasks
- `#api` - API development
- `#review` - Review and approval tasks
- `#low` - Low priority items

## 🔒 Privacy & Security

- **Server-Side API Key**: Your API key is securely stored in environment variables on your server
- **No Client-Side Exposure**: API key never exposed to the browser or client-side code
- **No Data Storage**: Task data is not stored on any external servers
- **Secure Communication**: All API calls are made server-side to Google's Gemini API
- **Environment Variables**: Sensitive configuration kept in `.env` file (excluded from git)

## 🛠️ Development

### Development Mode
```bash
npm run dev
```
This uses nodemon for automatic server restarts when files change.

### Available Scripts
- `npm start` - Start the production server
- `npm run dev` - Start development server with auto-restart
- `npm test` - Run tests (placeholder)

### Customization
You can easily customize the application by:
- Modifying the tag list in the prompt (server.js)
- Adjusting priority criteria in the AI prompt
- Changing the UI styling (public/style.css)
- Adding new export formats
- Modifying the Express server endpoints

## 📋 API Usage

The application uses Google's **Gemini 2.0 Flash** model via REST API for faster processing and better performance. Make sure you have:
- A valid Google account
- Access to Google AI Studio
- An active API key with Gemini API access

### Why Gemini 2.0 Flash?
- **Faster Processing**: Significantly quicker response times
- **Better Accuracy**: Improved understanding of task context and priorities
- **Cost Effective**: More efficient token usage
- **Enhanced Reasoning**: Better at categorizing and prioritizing complex tasks

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy to Vercel:**
   ```bash
   vercel
   ```

3. **Set Environment Variables:**
   ```bash
   vercel env add GEMINI_API_KEY
   # Enter your API key when prompted
   ```

4. **Redeploy with Environment Variables:**
   ```bash
   vercel --prod
   ```

### Environment Variables for Production
```bash
GEMINI_API_KEY=your_production_api_key
NODE_ENV=production
```

### Docker Deployment (Alternative)
Create a `Dockerfile`:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🐛 Troubleshooting

**Server Issues:**
- Check that your `.env` file exists and contains a valid `GEMINI_API_KEY`
- Ensure Node.js version 16+ is installed
- Verify the server is running on the correct port

**API Key Issues:**
- Ensure your API key is valid and has Gemini API access
- Check that you haven't exceeded your API quota
- Verify the API key is correctly set in the `.env` file

**Processing Errors:**
- Make sure your task notes contain actual tasks (not empty text)
- Try breaking down very long task lists into smaller chunks
- Check server logs for detailed error messages

**Export Issues:**
- Ensure you've processed tasks before trying to export
- Check that your browser allows file downloads

## 📄 License

This project is open source and available under the MIT License.

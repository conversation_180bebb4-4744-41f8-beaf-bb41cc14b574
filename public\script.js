let processedTasks = [];

// Check server health and API key status on page load
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const response = await fetch('/api/health');
        const health = await response.json();
        
        if (!health.apiKeyConfigured) {
            showError('⚠️ Gemini API key not configured on server. Please check your .env file.');
        }
        
        // Hide the API key input section since it's now server-side
        const apiKeySection = document.querySelector('.api-key-section');
        if (apiKeySection) {
            apiKeySection.style.display = 'none';
        }
        
    } catch (error) {
        showError('❌ Unable to connect to server. Please make sure the server is running.');
    }
});

async function processTasks() {
    const taskInput = document.getElementById('taskInput').value.trim();
    
    // Validation
    if (!taskInput) {
        showError('Please enter some task notes to process');
        return;
    }
    
    // Show loading state
    showLoading();
    hideError();
    hideResults();
    
    try {
        const response = await fetch('/api/process-tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                taskNotes: taskInput
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        processedTasks = data.tasks;
        displayResults(data.tasks);
        hideLoading();
        
    } catch (error) {
        hideLoading();
        showError(`Error processing tasks: ${error.message}`);
    }
}

function displayResults(tasks) {
    const taskList = document.getElementById('taskList');
    taskList.innerHTML = '';
    
    // Sort tasks by priority (highest first)
    const sortedTasks = [...tasks].sort((a, b) => b.priority - a.priority);
    
    sortedTasks.forEach((task, index) => {
        const taskElement = document.createElement('div');
        taskElement.className = 'task-item';
        
        const tagsHtml = task.tags.map(tag => `<span class="tag">${tag}</span>`).join('');
        const priorityText = getPriorityText(task.priority);
        
        taskElement.innerHTML = `
            <div class="task-summary">${task.summary}</div>
            <div class="task-meta">
                <div class="task-tags">${tagsHtml}</div>
                <div class="priority priority-${task.priority}">Priority ${task.priority} - ${priorityText}</div>
            </div>
        `;
        
        taskList.appendChild(taskElement);
    });
    
    showResults();
}

function getPriorityText(priority) {
    const priorityTexts = {
        5: 'Critical',
        4: 'High',
        3: 'Normal',
        2: 'Low',
        1: 'Trivial'
    };
    return priorityTexts[priority] || 'Unknown';
}

function exportJSON() {
    const dataStr = JSON.stringify(processedTasks, null, 2);
    downloadFile(dataStr, 'tasks.json', 'application/json');
}

function exportCSV() {
    const headers = ['Summary', 'Tags', 'Priority'];
    const csvRows = [headers.join(',')];
    
    processedTasks.forEach(task => {
        const row = [
            `"${task.summary.replace(/"/g, '""')}"`,
            `"${task.tags.join(', ')}"`,
            task.priority
        ];
        csvRows.push(row.join(','));
    });
    
    const csvContent = csvRows.join('\n');
    downloadFile(csvContent, 'tasks.csv', 'text/csv');
}

function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function showLoading() {
    document.getElementById('loading').classList.remove('hidden');
    document.getElementById('processBtn').disabled = true;
}

function hideLoading() {
    document.getElementById('loading').classList.add('hidden');
    document.getElementById('processBtn').disabled = false;
}

function showResults() {
    document.getElementById('results').classList.remove('hidden');
}

function hideResults() {
    document.getElementById('results').classList.add('hidden');
}

function showError(message) {
    const errorDiv = document.getElementById('error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

function hideError() {
    document.getElementById('error').classList.add('hidden');
}
